# E-commerce Spring Boot Application

A full-featured e-commerce web application built with Spring Boot, featuring product management, user authentication, and administrative controls. This application provides a complete online shopping experience with separate interfaces for customers and administrators.


## Author

**<PERSON><PERSON><PERSON>**
- GitHub: [@Janmejay3108](https://github.com/Janmejay3108)
- Email: <EMAIL>
- LinkedIn: [jan<PERSON><PERSON>-tiwari](https://www.linkedin.com/in/janmejay-tiwari/)
- Website: [<PERSON><PERSON><PERSON>](https://janmejaytiwari.vercel.app/)


## Features

- **Product Management**: Browse, search, and manage products with categories
- **User Authentication**: Secure login system with role-based access control
- **Admin Dashboard**: Complete administrative interface for managing:
  - Products (add, edit, delete)
  - Categories (create, update, remove)
  - Customer accounts and information
- **Responsive Design**: Clean and modern UI built with Bootstrap and JSP
- **Database Integration**: Automatic database setup with Hibernate ORM
- **Security**: Spring Security implementation with user roles (Admin/User)
- **IDE Support**: Compatible with both Eclipse and IntelliJ IDEA


## Getting Started

You can run the application locally by following these steps:

### Prerequisites

- Java 11 or higher
- Maven 3.6 or higher
- MySQL 8.0 or MariaDB
- IDE: IntelliJ IDEA (recommended) or Eclipse

### Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd E-commerce-project-springBoot-master2

# Navigate to the project directory
cd JtProject

# Configure database (see Database Configuration section)
# Edit src/main/resources/application.properties

# Run the application
mvn spring-boot:run
```

Then open `http://localhost:8080` in your browser.

**Default Login Credentials:**
- **Admin**: Username: `admin`, Password: `123`
- **User**: Username: `lisa`, Password: `765`

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd E-commerce-project-springBoot-master2/JtProject
```

2. Configure the database connection in `src/main/resources/application.properties`:
```properties
db.url=*******************************************************************
db.username=root
db.password=your_password
```

3. Initialize the database with sample data:
```bash
# Run the basedata.sql script in your MySQL client
mysql -u root -p ecommjava < basedata.sql
```

4. Run the application:
```bash
mvn spring-boot:run
```

5. Access the application:
Open your browser and go to `http://localhost:8080`

### IDE Configuration (IntelliJ IDEA)

If using IntelliJ IDEA, configure the working directory:

1. Click "Edit Configurations..." in the top right corner
2. Select the `JtSpringProjectApplication` configuration
3. Set "Working directory" to `$MODULE_WORKING_DIR$`
4. Click "Apply" and "OK"

## Usage

### For Customers

1. **Browse Products**: View all available products on the home page
2. **Product Details**: Click on products to see detailed information
3. **User Registration**: Create a new account via the registration page
4. **User Login**: Access your account with username and password

### For Administrators

1. **Admin Login**: Use admin credentials to access the admin panel
2. **Manage Products**: Add, edit, or delete products from the inventory
3. **Manage Categories**: Create and organize product categories
4. **View Customers**: Monitor registered users and their information
5. **Dashboard**: Access comprehensive admin dashboard at `/admin/Dashboard`

## Tech Stack

### Backend
- **Spring Boot 2.6.4** - Main application framework
- **Spring Security** - Authentication and authorization
- **Spring Data JPA** - Data persistence layer
- **Hibernate** - ORM framework
- **MySQL** - Database management system
- **Maven** - Dependency management and build tool

### Frontend
- **JSP (JavaServer Pages)** - Server-side rendering
- **Bootstrap** - CSS framework for responsive design
- **JSTL** - JSP Standard Tag Library
- **HTML/CSS/JavaScript** - Core web technologies

## 📁 Project Structure

```
JtProject/
├── src/
│   ├── main/
│   │   ├── java/com/jtspringproject/JtSpringProject/
│   │   │   ├── controller/          # REST controllers
│   │   │   ├── models/              # Entity classes
│   │   │   ├── services/            # Business logic layer
│   │   │   ├── dao/                 # Data access objects
│   │   │   ├── configuration/       # Security and app config
│   │   │   └── JtSpringProjectApplication.java
│   │   ├── resources/
│   │   │   └── application.properties
│   │   └── webapp/
│   │       └── views/               # JSP view templates
│   └── test/                        # Test classes
├── basedata.sql                     # Database initialization script
└── pom.xml                          # Maven configuration
```

## API Endpoints

### Public Endpoints
- `GET /` - Home page with product listings
- `GET /register` - User registration page
- `POST /newuserregister` - Process user registration
- `GET /login` - Login page

### Admin Endpoints
- `GET /admin/Dashboard` - Admin dashboard
- `GET /admin/products` - Product management
- `GET /admin/categories` - Category management
- `GET /admin/customers` - Customer management

## Database Configuration

The application uses MySQL as the default database. Configure your database connection in `application.properties`:

```properties
# Database Configuration
db.driver=com.mysql.cj.jdbc.Driver
db.url=*******************************************************************
db.username=root
db.password=your_password

# Hibernate Configuration
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show_sql=true
hibernate.hbm2ddl.auto=update
```

**Note**: The application will automatically create the database if it doesn't exist, thanks to the `createDatabaseIfNotExist=true` parameter.

## Security Features

- **Role-based Access Control**: Separate interfaces for Admin and User roles
- **Spring Security Integration**: Secure authentication and authorization
- **Password Encryption**: User passwords are securely encrypted
- **Session Management**: Proper session handling and logout functionality
- **CSRF Protection**: Built-in protection against cross-site request forgery

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## Acknowledgments

- Spring Boot community for excellent documentation
- Bootstrap team for the responsive CSS framework
- All contributors and users of this project
